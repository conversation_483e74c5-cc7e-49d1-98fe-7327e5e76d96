<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{{period_tracker_page_title}}</title>
  <link rel="icon" href="{{period_tracker_favicon_path}}" type="image/x-icon">
  <link href="https://fonts.googleapis.com/css2?family=Work+Sans:wght@400;600&family=Roboto+Condensed:wght@400;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="style.css">
</head>
<body>
  <header>
    <div class="navbar">
      <div class="logo">
        <a href="{{period_tracker_home_url}}">
          <img width="200" src="{{period_tracker_logo_path}}" alt="{{period_tracker_logo_alt}}" />
        </a>
      </div>
      <div class="nav">
        <div class="language-switcher" id="language-switcher">
          <select id="language-select">
            <!-- Options will be populated by JS -->
          </select>
        </div>
        <a href="{{period_tracker_talk_to_link}}" class="talk-to-august">{{period_tracker_talk_to_text}}</a>
      </div>
    </div>
  </header>

  <div class="main-container">
    <div class="content-layout">
      <!-- Left Content -->
      <div class="content-section">
        <div class="content-header">
          <h1>{{period_calculator_content_title}}</h1>
          <p class="content-description">{{period_calculator_content_description}}</p>
        </div>
        <div class="cycle-facts">
          <h3>{{period_tracker_facts_title}}</h3>
          <ul>
            <li>{{period_tracker_fact_1}}</li>
            <li>{{period_tracker_fact_2}}</li>
            <li>{{period_tracker_fact_3}}</li>
            <li>{{period_tracker_fact_4}}</li>
          </ul>
        </div>
        <div class="motivational-quote">
          {{period_tracker_motivational_quote}}
        </div>
        <div class="features-list">
          <div class="feature-item">
            <div class="feature-icon">{{period_tracker_feature_icon_1}}</div>
            <div class="feature-text">
              <div class="feature-title">{{period_tracker_feature_title_1}}</div>
              <div class="feature-desc">{{period_tracker_feature_desc_1}}</div>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">{{period_tracker_feature_icon_2}}</div>
            <div class="feature-text">
              <div class="feature-title">{{period_tracker_feature_title_2}}</div>
              <div class="feature-desc">{{period_tracker_feature_desc_2}}</div>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">{{period_tracker_feature_icon_3}}</div>
            <div class="feature-text">
              <div class="feature-title">{{period_tracker_feature_title_3}}</div>
              <div class="feature-desc">{{period_tracker_feature_desc_3}}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Calculator -->
      <div class="calculator-section bmi-style-card">
        <div class="bmi-card-header">
          <div class="bmi-card-title-group">
            <h2 class="bmi-card-title">{{period_tracker_calculator_title}}</h2>
            <span class="bmi-card-subtitle">{{period_tracker_calculator_subtitle}}</span>
          </div>
          <button type="button" id="resetBtn" class="bmi-reset-link">{{period_tracker_reset_button_text}}</button>
        </div>
        <form id="trackerForm">
          <div class="bmi-radio-group">
            <label><input type="radio" name="units" value="days" checked disabled>{{period_tracker_unit_label}}</label>
          </div>
          <div class="form-group">
            <label for="lastPeriod">{{period_tracker_last_period_label}}</label>
            <input type="date" id="lastPeriod" class="form-input" required>
          </div>
          <div class="form-group">
            <label for="periodLength">{{period_tracker_period_length_label}}</label>
            <div class="input-row">
              <input type="number" id="periodLength" class="form-input" min="1" max="15" placeholder="{{period_tracker_period_length_placeholder}}" required>
              <span class="input-unit">{{period_tracker_period_length_unit}}</span>
            </div>
          </div>
          <div class="form-group">
            <label for="cycleLength">{{period_tracker_cycle_length_label}}</label>
            <div class="input-row">
              <input type="number" id="cycleLength" class="form-input" min="21" max="45" placeholder="{{period_tracker_cycle_length_placeholder}}" required>
              <span class="input-unit">{{period_tracker_cycle_length_unit}}</span>
            </div>
          </div>
          <button type="submit" class="calculate-btn bmi-calc-btn">{{period_tracker_calculate_button_text}}</button>
        </form>
      </div>
    </div>

    <!-- Results Section -->
    <div class="results-section bmi-results-card" id="resultsSection">
      <div class="results-header">
        <p class="results-subtitle">{{period_tracker_results_subtitle}}</p>
      </div>
      <div class="legend">
        <div class="legend-item">
          <div class="legend-dot pre-period"></div>
          <span>{{period_tracker_legend_pre_period}}</span>
        </div>
        <div class="legend-item">
          <div class="legend-dot period"></div>
          <span>{{period_tracker_legend_period_days}}</span>
        </div>
        <div class="legend-item">
          <div class="legend-dot post-period"></div>
          <span>{{period_tracker_legend_post_period}}</span>
        </div>
        <div class="legend-item">
          <div class="legend-dot ovulation"></div>
          <span>{{period_tracker_legend_ovulation}}</span>
        </div>
        <div class="legend-item">
          <div class="legend-dot normal"></div>
          <span>{{period_tracker_legend_normal}}</span>
        </div>
      </div>
      <div id="calendarContainer" class="bmi-calendar-center"></div>
      <div class="result-note">
        <strong>{{period_tracker_note_label}}</strong> {{period_tracker_note_text}}
      </div>
    </div>
  </div>
  <script src="script.js"></script>
</body>
</html>
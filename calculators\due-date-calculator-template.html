<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{pregnancy_title}</title>
    <meta name="description" content="{pregnancy_meta_description}" />
    <meta name="keywords" content="{pregnancy_meta_keywords}" />
    <meta name="robots" content="index, follow" />
    <meta property="og:title" content="{pregnancy_og_title}" />
    <meta property="og:description" content="{pregnancy_og_description}" />
    <meta property="og:type" content="website" />
    <link
      rel="canonical"
      href="https://www.meetaugust.ai/{{lang}}/calculators/pregnancy-calculator"
    />
    <link
      rel="icon"
      href="/{{lang}}/calculators/assets/favicon.png"
      type="image/x-icon"
    />
    <link
      rel="stylesheet"
      href="/{{lang}}/calculators/pregnancy-calculator/style.css"
    />
    <style>
      /* Language Switcher MUI-like Styles */
      .language-switcher {
        display: flex;
        align-items: center;
        margin-right: 16px;
      }
      #language-select {
        min-width: 120px;
        border: 1px solid #e5e7eb;
        border-radius: 5px;
      height: 45px;
        padding: 8px 16px;
        font-size: 1rem;
        color: #374151;
        background: #fff;
        outline: none;
        transition: border-color 0.2s;
        box-shadow: none;
        appearance: none;
        cursor: pointer;
      }
      #language-select:focus {
        border-color: #4caf50;
        box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.15);
      }
      #language-select option {
        font-size: 1rem;
        padding: 10px 16px;
      }
      @media (max-width: 600px) {
        #language-select {
          min-width: 80px;
          font-size: 0.875rem;
          padding: 6px 8px;
        }
        #language-select option {
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <header>
      <div class="navbar">
        <div class="logo">
          <a href="/{{lang}}/calculators/">
            <img
              width="200"
              src="/{{lang}}/calculators/assets/august_logo_green_nd4fn9.svg"
              alt="{{logo_alt}}"
            />
          </a>
        </div>
        <div class="nav">
          <div class="language-switcher" id="language-switcher">
            <select id="language-select">
              <!-- Options will be populated by JS -->
            </select>
          </div>
          <a
            href="https://app.meetaugust.ai/join/app?utm=pregnancy"
            class="talk-to-august"
            >{{talk_to_august}}</a
          >
        </div>
      </div>
    </header>
    <div class="container">
      <div class="hero">
        <h1>{due_date_calculator_content_title}</h1>
        <p>{due_date_calculator_description}</p>
      </div>

      <div class="main-content">
        <div class="info-card">
          <div class="feature-list">
            <span class="check-mark">✓</span>
            <span class="feature-text">{pregnancy_trusted_by}</span>
          </div>

          <h2>{pregnancy_why_use_title}</h2>
          <p>{pregnancy_why_use_description}</p>

          <h3>{pregnancy_key_features_title}</h3>
          <ul>
            <li>{pregnancy_feature_instant_due_date}</li>
            <li>{pregnancy_feature_gestational_age}</li>
            <li>{pregnancy_feature_interface}</li>
            <li>{pregnancy_feature_medical_accuracy}</li>
            <li>{pregnancy_feature_free}</li>
          </ul>
        </div>

        <div class="calculator-card">
          <h2 class="calculator-title">{pregnancy_calculator_title}</h2>

          <form name="pregnancy_form">
            <div class="form-group">
              <label for="current_date_picker">{pregnancy_current_date_label}</label>
              <div class="date-inputs">
                <input type="date" id="current_date_picker" name="current_date_picker" aria-label="{pregnancy_current_date_aria}" style="padding: 6px 8px; font-size: 16px; border: 1px solid #e5e7eb; border-radius: 6px;" />
              </div>
            </div>

            <div class="form-group">
              <label for="lmp_date_picker">{pregnancy_lmp_date_label}</label>
              <div class="date-inputs">
                <input type="date" id="lmp_date_picker" name="lmp_date_picker" aria-label="{pregnancy_lmp_date_aria}" style="padding: 6px 8px; font-size: 16px; border: 1px solid #e5e7eb; border-radius: 6px;" />
              </div>
            </div>

            <div class="button-group">
              <button type="button" class="btn" onclick="setToToday()">
                {pregnancy_set_to_today}
              </button>
              <button type="button" class="btn" onclick="clearResults()">
                {pregnancy_clear_results}
              </button>
            </div>

            <div class="results-section">
              <div class="result-item">
                <div class="result-label">{pregnancy_due_date_label}</div>
                <div class="result-value" id="due_date_result">-</div>
              </div>
              <div class="result-item">
                <div class="result-label">
                  {pregnancy_gestational_age_label}
                </div>
                <div class="result-value" id="gestational_age_result">-</div>
              </div>
            </div>
          </form>
        </div>
      </div>

      <div class="faq-section">
        <h2>{pregnancy_faq_title}</h2>

        <div class="faq-item">
          <div class="faq-question">{pregnancy_faq_accuracy_question}</div>
          <div class="faq-answer">{pregnancy_faq_accuracy_answer}</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">{pregnancy_faq_lmp_memory_question}</div>
          <div class="faq-answer">{pregnancy_faq_lmp_memory_answer}</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            {pregnancy_faq_irregular_periods_question}
          </div>
          <div class="faq-answer">{pregnancy_faq_irregular_periods_answer}</div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            {pregnancy_faq_prenatal_appointment_question}
          </div>
          <div class="faq-answer">
            {pregnancy_faq_prenatal_appointment_answer}
          </div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            {pregnancy_faq_gestational_fetal_question}
          </div>
          <div class="faq-answer">{pregnancy_faq_gestational_fetal_answer}</div>
        </div>
      </div>
    </div>

    <script src="script.js"></script>
    <script>
      const locales = [
        "en",
        "fr",
        "de",
        "es",
        "it",
        "pt",
        "ru",
        "ja",
        "ko",
        "he",
        "bg",
        "fi",
        "hr",
        "lv",
        "mk",
        "mr",
        "sk",
        "sl",
        "sr",
        "tl",
        "el",
      ];
      const languageNames = {
        en: "English",
        fr: "Français",
        de: "Deutsch",
        es: "Español",
        it: "Italiano",
        pt: "Português",
        ru: "Русский",
        ja: "日本語",
        ko: "한국어",
        he: "עברית",
        bg: "Български",
        fi: "Suomi",
        hr: "Hrvatski",
        lv: "Latviešu",
        mk: "Македонски",
        mr: "मराठी",
        sk: "Slovenčina",
        sl: "Slovenščina",
        sr: "Српски",
        tl: "Tagalog",
        el: "Ελληνικά",
      };
      const select = document.getElementById("language-select");
      const currentLang = window.location.pathname.split("/")[1] || "en";
      locales.forEach((l) => {
        const opt = document.createElement("option");
        opt.value = l;
        opt.textContent = languageNames[l] || l;
        if (l === currentLang) opt.selected = true;
        select.appendChild(opt);
      });
      select.addEventListener("change", function () {
        const newLang = this.value;
        let path = window.location.pathname.split("/");
        if (locales.includes(path[1])) {
          path[1] = newLang;
        } else {
          path = ["", newLang, ...path.slice(1)];
        }
        localStorage.setItem("preferredLang", newLang);
        window.location.pathname = path.join("/");
      });
      // Persist language choice
      if (
        localStorage.getItem("preferredLang") &&
        localStorage.getItem("preferredLang") !== currentLang
      ) {
        select.value = localStorage.getItem("preferredLang");
        select.dispatchEvent(new Event("change"));
      }
    </script>
  </body>
</html>
